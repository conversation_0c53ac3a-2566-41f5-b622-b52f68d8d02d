[project]
name = "agenticseek"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "adaptive-classifier>=0.0.10",
    "aiofiles>=24.1.0",
    "anyio>=3.5.0,<5",
    "celery>=5.5.1",
    "certifi==2025.4.26",
    "chromedriver-autoinstaller>=0.6.4",
    "colorama>=0.4.6",
    "distro>=1.7.0,<2",
    "fake-useragent>=2.1.0",
    "fastapi>=0.115.12",
    "flask>=3.1.0",
    "httpx>=0.27,<0.29",
    "ipython>=8.13.0",
    "jiter>=0.4.0,<1",
    "kokoro==0.9.4",
    "langid>=1.1.6",
    "librosa>=0.10.2.post1",
    "markdownify>=1.1.0",
    "numpy>=1.24.4",
    "ollama>=0.4.7",
    "openai>=1.84.0",
    "ordered-set>=4.1.0",
    "playsound3>=1.0.0",
    "protobuf>=3.20.3",
    "pyaudio>=0.2.14",
    "pydantic>=2.10.6",
    "pydantic-core>=2.27.2",
    "pypdf>=5.4.0",
    "pypinyin>=0.54.0",
    "pyreadline3>=3.5.4",
    "python-dotenv>=1.0.0",
    "requests>=2.31.0",
    "sacremoses>=0.0.53",
    "scipy>=1.9.3",
    "selenium>=4.27.1",
    "selenium-stealth>=1.0.6",
    "sentencepiece>=0.2.0",
    "setuptools>=75.6.0",
    "sniffio>=1.3.1",
    "soundfile>=0.13.1",
    "termcolor>=2.4.0",
    "text2emotion>=0.0.5",
    "together>=1.5.0",
    "torch>=2.4.1",
    "tqdm>4",
    "transformers>=4.46.3",
    "undetected-chromedriver>=3.5.5",
    "uvicorn>=0.34.0",
]
