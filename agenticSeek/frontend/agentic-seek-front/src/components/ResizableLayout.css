.resizable-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.resizable-left,
.resizable-right {
  height: 100%;
  overflow: hidden;
  min-width: 0;
}

.resize-handle {
  width: 8px;
  background-color: transparent;
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 2px;
  transition: background-color 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.resize-handle:hover {
  background-color: var(--accent);
}

.resize-handle-line {
  width: 2px;
  height: 40px;
  background-color: var(--border);
  border-radius: 1px;
  transition: all 0.2s ease;
}

.resize-handle:hover .resize-handle-line {
  background-color: var(--accent-foreground);
  height: 60px;
}

.resizable-container.dragging .resize-handle {
  background-color: var(--accent);
}

.resizable-container.dragging .resize-handle-line {
  background-color: var(--accent-foreground);
  height: 100vh;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .resizable-container {
    flex-direction: column;
  }

  .resizable-left,
  .resizable-right {
    width: 100% !important;
    height: 50vh;
  }

  .resize-handle {
    display: none;
  }
}
